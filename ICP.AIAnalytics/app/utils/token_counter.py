import tiktoken
from typing import List, Dict, Any, Optional
from langchain.schema import BaseMessage, SystemMessage, HumanMessage

class TokenCounter:
    """Utility class for counting tokens using tiktoken."""
    
    def __init__(self, model_name: str = "gpt-4o"):
        """Initialize with the appropriate encoding for the model."""
        try:
            self.encoding = tiktoken.encoding_for_model(model_name)
        except KeyError:
            # Fallback to cl100k_base for newer models
            self.encoding = tiktoken.get_encoding("cl100k_base")
        
        self.model_name = model_name
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in a text string."""
        if not text:
            return 0
        return len(self.encoding.encode(text))
    
    def count_message_tokens(self, messages: List[BaseMessage]) -> int:
        """Count tokens in a list of LangChain messages."""
        total_tokens = 0
        
        for message in messages:
            # Add tokens for the message content
            total_tokens += self.count_tokens(message.content)
            
            # Add overhead tokens for message formatting
            if isinstance(message, SystemMessage):
                total_tokens += 4  # System message overhead
            elif isinstance(message, HumanMessage):
                total_tokens += 4  # Human message overhead
            else:
                total_tokens += 4  # Default message overhead
        
        # Add overhead for the conversation
        total_tokens += 2  # Conversation overhead
        
        return total_tokens
    
    def estimate_response_tokens(self, max_tokens: int, safety_margin: int = 100) -> int:
        """Estimate available tokens for response with safety margin."""
        return max(0, max_tokens - safety_margin)
    
    def truncate_to_token_limit(self, text: str, max_tokens: int) -> str:
        """Truncate text to fit within token limit."""
        if not text:
            return text
            
        tokens = self.encoding.encode(text)
        if len(tokens) <= max_tokens:
            return text
        
        # Truncate tokens and decode back to text
        truncated_tokens = tokens[:max_tokens]
        return self.encoding.decode(truncated_tokens)
    
    def split_text_by_tokens(self, text: str, chunk_size: int, overlap: int = 0) -> List[str]:
        """Split text into chunks based on token count."""
        if not text:
            return []
        
        tokens = self.encoding.encode(text)
        chunks = []
        
        start = 0
        while start < len(tokens):
            end = min(start + chunk_size, len(tokens))
            chunk_tokens = tokens[start:end]
            chunk_text = self.encoding.decode(chunk_tokens)
            chunks.append(chunk_text)
            
            # Move start position with overlap
            start = end - overlap
            if start >= len(tokens):
                break
        
        return chunks
    
    def get_token_info(self, text: str) -> Dict[str, Any]:
        """Get detailed token information for debugging."""
        token_count = self.count_tokens(text)
        char_count = len(text)
        
        return {
            "token_count": token_count,
            "character_count": char_count,
            "tokens_per_char": token_count / char_count if char_count > 0 else 0,
            "model": self.model_name,
            "encoding": self.encoding.name
        }